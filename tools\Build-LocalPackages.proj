<Project DefaultTargets="BuildPackages">
  <Import Project="..\Directory.Build.props" />

  <PropertyGroup>
    <Configuration Condition="'$(Configuration)' == ''">Debug</Configuration>
  </PropertyGroup>

  <!-- List of projects that produce NuGet packages -->
  <ItemGroup>
    <PackableProject Include="..\src\PreviewFramework\PreviewFramework.csproj" />
    <PackableProject Include="..\src\PreviewFramework.SharedModel\PreviewFramework.SharedModel.csproj" />
    <PackableProject Include="..\src\tooling\PreviewFramework.DevTools\PreviewFramework.DevTools.csproj" />
    <PackableProject Include="..\src\platforms\PreviewFramework.App.Maui\PreviewFramework.App.Maui.csproj" />
    <PackableProject Include="..\src\platforms\PreviewFramework.App.Wpf\PreviewFramework.App.Wpf.csproj" />
  </ItemGroup>

  <!-- Clean package output directory -->
  <Target Name="CleanPackageOutput">
    <!-- Clear cached packages from NuGet cache -->
    <PropertyGroup>
      <NuGetPackagesPath Condition="'$(NUGET_PACKAGES)' != ''">$(NUGET_PACKAGES)</NuGetPackagesPath>
      <NuGetPackagesPath Condition="'$(NuGetPackagesPath)' == '' AND '$(USERPROFILE)' != ''">$(USERPROFILE)\.nuget\packages</NuGetPackagesPath>
      <NuGetPackagesPath Condition="'$(NuGetPackagesPath)' == '' AND '$(HOME)' != ''">$(HOME)/.nuget/packages</NuGetPackagesPath>
    </PropertyGroup>

    <ItemGroup>
      <CachedPackageDirectories Include="$(NuGetPackagesPath)\previewframework" />
      <CachedPackageDirectories Include="$(NuGetPackagesPath)\previewframework.sharedmodel" />
      <CachedPackageDirectories Include="$(NuGetPackagesPath)\previewframework.devtools" />
      <CachedPackageDirectories Include="$(NuGetPackagesPath)\previewframework.app.maui" />
      <CachedPackageDirectories Include="$(NuGetPackagesPath)\previewframework.app.wpf" />
    </ItemGroup>

    <Message Text="Clearing cached packages from NuGet cache: $(NuGetPackagesPath)" Importance="high" />
    <RemoveDir Directories="@(CachedPackageDirectories)" Condition="Exists('%(Identity)')" ContinueOnError="true" />

    <Message Text="Cleaning package output directory: $(PackageOutputPath)" Importance="high" />
    <RemoveDir Directories="$(PackageOutputPath)" Condition="Exists('$(PackageOutputPath)')" />
  </Target>

  <!-- Main target that builds packages -->
  <Target Name="BuildPackages" DependsOnTargets="CleanPackageOutput">
    <Message Text="Building all NuGet packages in $(Configuration) configuration..." Importance="high" />

    <!-- Build AppBuildTasks first as it's needed by SharedModel -->
    <MSBuild Projects="..\src\PreviewFramework.AppBuildTasks\PreviewFramework.AppBuildTasks.csproj"
             Targets="Build"
             Properties="Configuration=$(Configuration)" />

    <!-- Also explicitly build PreviewFramework.DevToolsApp, which is included in PreviewFramework.DevTools -->
    <MSBuild Projects="..\src\tooling\PreviewFramework.DevToolsApp\PreviewFramework.DevToolsApp.csproj"
             Targets="Build"
             Properties="Configuration=$(Configuration)" />

    <!-- Pack all projects -->
    <MSBuild Projects="@(PackableProject)"
             Targets="Pack"
             Properties="Configuration=$(Configuration)"
             ContinueOnError="false" />

    <Message Text="Packages built to: $(PackageOutputPath)" Importance="high" />
  </Target>

  <!-- Alias common target names to BuildPackages -->
  <Target Name="Build" DependsOnTargets="BuildPackages" />
  <Target Name="Pack" DependsOnTargets="BuildPackages" />
</Project>

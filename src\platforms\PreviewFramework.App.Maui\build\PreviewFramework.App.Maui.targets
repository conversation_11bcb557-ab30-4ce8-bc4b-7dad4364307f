<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Target Name="GeneratePreviewAppSettings"
          BeforeTargets="CoreCompile"
          Condition="'$(BuildingProject)' != 'false'"
          Inputs="$(MSBuildProjectFullPath)"
          Outputs="$(IntermediateOutputPath)PreviewAppInitializer.g.cs;$(UserProfile)\.previewframework\devToolsConnectionSettings.json">
    <GeneratePreviewAppSettingsTask
      ProjectPath="$(MSBuildProjectFullPath)"
      OutputPath="$(IntermediateOutputPath)PreviewAppInitializer.g.cs"
      PlatformPreviewApplication="PreviewFramework.App.Maui.MauiPreviewApplication.Instance" />
    <ItemGroup>
      <Compile Include="$(IntermediateOutputPath)PreviewAppInitializer.g.cs" />
    </ItemGroup>
  </Target>
</Project>
